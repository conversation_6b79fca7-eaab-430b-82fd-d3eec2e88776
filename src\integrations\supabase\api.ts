/**
 * This file contains API functions for interacting with Supabase.
 * For consistency, all database calls should go through these functions
 * rather than directly calling supabase client from components.
 */
import { supabase } from "./client";
import { ExpenseCategory } from "./schema";

// Types for API functions - commented out to avoid unused interface warnings
// interface ExpenseData {
//   title: string;
//   description?: string;
//   status?: string;
// }

// Modified LineItemData to make amount required but with proper default handling
// interface LineItemData {
//   id?: string;
//   expense_id?: string;
//   amount: number; // Required but we'll handle defaults in functions
//   currency: string;
//   date: string;
//   category_id?: string;
//   description?: string;
//   receipt_url?: string;
//   receipt_file?: any; // Added to match what's being passed in the form
// }

interface CategoryData {
  name: string;
  description?: string | null;
  active: boolean;
}

// Updated UserProfileData to include department_id AND spending_policy_id
interface UserProfileData {
  first_name?: string;
  last_name?: string;
  role?: string;
  department_id?: string | null;
  manager_id?: string | null;
  spending_policy_id?: string | null; // Added this field to fix the TypeScript error
}

/**
 * Create a new expense with line items
 * @param expenseData Basic expense data
 * @param lineItems Array of line items for the expense
 * @returns ID of the created expense or null on error
 */
export async function createExpense(
  expenseData: any,
  lineItems?: any[]
): Promise<string | null> {
  // Note: Use expense schema instead of public schema
  try {
    console.log("Creating expense with data:", expenseData);
    console.log("Line items:", lineItems);
    
    // Get current authenticated user
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.error("User not authenticated");
      return null;
    }
    
    // Calculate the total amount from line items or use 0 as default
    // This ensures the amount field is never null
    const totalAmount = lineItems && lineItems.length > 0
      ? lineItems.reduce((sum, item) => sum + (Number(item.amount) || 0), 0)
      : 0;
    
    // Get date from the first line item, or use current date as fallback
    // This ensures the date field is never null
    const expenseDate = lineItems && lineItems.length > 0
      ? lineItems[0].date 
      : new Date().toISOString().split('T')[0];
    
    // First, create the expense
    const { data, error } = await supabase
      .from("expenses")
      .insert({
        title: expenseData.title,
        description: expenseData.description,
        status: "draft",
        created_at: new Date().toISOString(),
        created_by: user.id, // Use the user's ID from the authentication
        user_id: user.id, // Ensure we still set the user_id as well for RLS
        amount: totalAmount, // Add the amount from line items total
        date: expenseDate // Add the date from first line item or today's date
      })
      .select('id')
      .single();

    if (error) {
      console.error("Error inserting expense:", error);
      throw error;
    }
    if (!data) throw new Error("Failed to retrieve created expense ID");

    const expenseId = data.id;
    console.log("Created expense with ID:", expenseId);

    // If we have line items, add them
    if (lineItems && lineItems.length > 0) {
      // Make sure all line items have a valid amount and expense_id
      const formattedLineItems = lineItems.map(item => {
        console.log("Processing line item for insertion:", item);
        return {
          expense_id: expenseId,
          // Ensure amount is always a number and not null/undefined
          amount: Number(item.amount) || 0,
          currency: item.currency,
          date: item.date,
          category_id: item.category_id,
          description: item.description,
          receipt_url: item.receipt_url // Make sure this is included
        };
      });

      console.log("Inserting formatted line items:", formattedLineItems);
      const { error: lineItemError, data: lineItemsData } = await supabase
        .from("expense_line_items")
        .insert(formattedLineItems)
        .select();

      if (lineItemError) {
        console.error("Error inserting line items:", lineItemError);
        throw lineItemError;
      }
      
      console.log("Line items inserted successfully:", lineItemsData);
    }

    return expenseId;
  } catch (error) {
    console.error("Error creating expense with line items:", error);
    return null;
  }
}

/**
 * Get an expense by ID including its line items
 * @param id Expense ID
 * @returns Expense data with line items or null if not found
 */
export async function getExpenseById(id: string) {
  try {
    // First get the expense basic data
    const { data: expense, error } = await supabase
      .from("expenses")
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;
    if (!expense) return null;

    // Get line items for this expense
    const { data: lineItems, error: lineItemsError } = await supabase
      .from("expense_line_items")
      .select("*")
      .eq("expense_id", id);

    if (lineItemsError) throw lineItemsError;

    // Return combined data
    return {
      ...expense,
      lineItems: lineItems || []
    };
  } catch (error) {
    console.error("Error getting expense:", error);
    return null;
  }
}

/**
 * Update an expense and its line items
 * @param id Expense ID
 * @param expenseData Updated expense data
 * @param lineItems Updated line items
 * @returns true if successful, false otherwise
 */
export async function updateExpenseWithLineItems(
  id: string, 
  expenseData: any, 
  lineItems: any[]
): Promise<boolean> {
  try {
    console.log("Updating expense with ID:", id);
    console.log("Updated expense data:", expenseData);
    console.log("Updated line items:", lineItems);
    
    // Calculate total amount from line items
    const totalAmount = lineItems.reduce(
      (sum, item) => sum + (Number(item.amount) || 0), 
      0
    );
    
    // First update the expense
    const { error: expenseError } = await supabase
      .from("expenses")
      .update({
        ...expenseData,
        amount: totalAmount,
        updated_at: new Date().toISOString()
      })
      .eq("id", id);

    if (expenseError) {
      console.error("Error updating expense:", expenseError);
      throw expenseError;
    }

    // Handle line items - this requires more complex logic to handle updates, inserts and deletes
    // 1. Get existing line items
    const { data: existingLineItems, error: fetchError } = await supabase
      .from("expense_line_items")
      .select("id")
      .eq("expense_id", id);

    if (fetchError) {
      console.error("Error fetching existing line items:", fetchError);
      throw fetchError;
    }
    
    console.log("Existing line item IDs:", existingLineItems);

    // 2. Determine which items to update vs insert
    const existingIds = new Set((existingLineItems || []).map(item => item.id));
    const itemsToUpdate = lineItems.filter(item => item.id && existingIds.has(item.id));
    const itemsToInsert = lineItems.filter(item => !item.id || !existingIds.has(item.id));
    
    console.log("Items to update:", itemsToUpdate);
    console.log("Items to insert:", itemsToInsert);
    
    // 3. Create a set of IDs we're keeping, so we can delete the rest
    const keepIds = new Set(itemsToUpdate.map(item => item.id));
    const idsToDelete = [...existingIds].filter(id => !keepIds.has(id));
    
    console.log("IDs to delete:", idsToDelete);

    // 4. Update existing items
    for (const item of itemsToUpdate) {
      if (!item.id) continue;
      
      console.log("Updating line item:", item);
      const { error, data } = await supabase
        .from("expense_line_items")
        .update({
          amount: Number(item.amount) || 0, // Ensure amount is always a number
          currency: item.currency,
          date: item.date,
          category_id: item.category_id,
          description: item.description,
          receipt_url: item.receipt_url // Make sure receipt_url is included
        })
        .eq("id", item.id)
        .select();

      if (error) {
        console.error("Error updating line item:", error);
        throw error;
      }
      
      console.log("Updated line item result:", data);
    }

    // 5. Insert new items
    if (itemsToInsert.length > 0) {
      const formattedItems = itemsToInsert.map(item => {
        console.log("Formatting new line item for insertion:", item);
        return {
          expense_id: id,
          amount: Number(item.amount) || 0, // Ensure amount is always a number
          currency: item.currency || 'USD', // Default currency
          date: item.date || new Date().toISOString().split('T')[0], // Default date to today
          category_id: item.category_id,
          description: item.description,
          receipt_url: item.receipt_url // Make sure receipt_url is included
        };
      });

      console.log("Inserting new line items:", formattedItems);
      const { error, data } = await supabase
        .from("expense_line_items")
        .insert(formattedItems)
        .select();

      if (error) {
        console.error("Error inserting new line items:", error);
        throw error;
      }
      
      console.log("Inserted line items result:", data);
    }

    // 6. Delete removed items
    if (idsToDelete.length > 0) {
      for (const id of idsToDelete) {
        console.log("Deleting line item with ID:", id);
        const { error, data } = await supabase
          .from("expense_line_items")
          .delete()
          .eq("id", id)
          .select();
        
        if (error) {
          console.error("Error deleting line item:", error);
          throw error;
        }
        
        console.log("Deleted line item result:", data);
      }
    }

    console.log("Expense update completed successfully");
    return true;
  } catch (error) {
    console.error("Error updating expense with line items:", error);
    return false;
  }
}

/**
 * Upload a receipt for a line item
 * @param file File to upload
 * @param lineItemId ID of the line item
 * @returns URL of the uploaded file or null on error
 */
export async function uploadLineItemReceipt(
  file: File,
  lineItemId: string
): Promise<string | null> {
  try {
    // Generate a unique file path
    const fileExt = file.name.split('.').pop();
    const fileName = `receipts/${lineItemId}/${Date.now()}.${fileExt}`;
    
    // Upload the file to Storage
    const { error: uploadError } = await supabase.storage
      .from('expense-imgs')
      .upload(fileName, file, {
        upsert: true,
      });
      
    if (uploadError) throw uploadError;
    
    // Get the public URL
    const { data: urlData } = supabase.storage
      .from('expense-imgs')
      .getPublicUrl(fileName);
      
    return urlData.publicUrl;
  } catch (error) {
    console.error("Error uploading receipt:", error);
    return null;
  }
}

/**
 * Get recent expenses for the current user
 * @param limit Number of expenses to return
 * @returns Array of expenses
 */
export async function getRecentExpenses(limit = 10) {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return [];

    const { data, error } = await supabase
      .from("expenses")
      .select(`
        *,
        profiles:user_id (
          first_name,
          last_name
        )
      `)
      .eq("user_id", user.id)
      .order("created_at", { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error("Error fetching recent expenses:", error);
    return [];
  }
}

/**
 * Get pending expenses for review (for managers and admins)
 * @returns Array of pending expenses
 */
export async function getPendingExpenses(limit = 20) {
  try {
    const { data, error } = await supabase
      .from("expenses")
      .select(`
        *,
        profiles:user_id (
          first_name,
          last_name
        )
      `)
      .eq("status", "submitted")
      .order("created_at", { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error("Error fetching pending expenses:", error);
    return [];
  }
}

/**
 * Update the status of an expense
 * @param expenseId ID of the expense to update
 * @param status New status
 * @returns boolean indicating success or failure
 */
export async function updateExpenseStatus(expenseId: string, status: string, Comment: string): Promise<boolean> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return false;
    
    // First, get the current status of the expense
    const { data: currentExpenseData, error: fetchError } = await supabase
      .from("expenses")
      .select("status")
      .eq("id", expenseId)
      .single();
    
    if (fetchError) throw fetchError;
    
    // Update the expense status
    const { error } = await supabase
      .from("expenses")
      .update({ 
        status: status,
        updated_at: new Date().toISOString()
      })
      .eq("id", expenseId);

    if (error) throw error;

    // Only create a history entry if we have both the current status and it's different from the new status
    if (currentExpenseData && currentExpenseData.status !== status) {
      await supabase
        .from("expense_history")
        .insert({
          expense_id: expenseId,
          action: "status_change",
          old_status: currentExpenseData.status,
          new_status: status,
          user_id: user.id,
          notes: Comment
        });
    }

    return true;
  } catch (error) {
    console.error("Error updating expense status:", error);
    return false;
  }
}

/**
 * Get the spending policy for the current user
 * @returns Policy text or null if not found
 */
export async function getUserGroupPolicy() {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return null;

    // Get the user's profile with spending_policy_id
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("spending_policy_id")
      .eq("id", user.id)
      .single();

    if (profileError || !profile || !profile.spending_policy_id) {
      console.log("No spending policy found in user profile:", profileError || "No policy ID");
      return null;
    }

    // Now get the policy based on the spending_policy_id from the profile
    const { data: policy, error: policyError } = await supabase
      .from("spending_groups")
      .select("policy_text")
      .eq("id", profile.spending_policy_id)
      .single();

    if (policyError) {
      console.error("Error fetching spending policy:", policyError);
      throw policyError;
    }
    
    return policy;
  } catch (error) {
    console.error("Error getting user spending policy:", error);
    return null;
  }
}

/**
 * Get statistics for admin dashboard
 * @returns Object with stats
 */
export async function getAdminStats() {
  try {
    // Get total users
    const { count: totalUsers, error: usersError } = await supabase
      .from("profiles")
      .select("id", { count: "exact", head: true });
    
    if (usersError) throw usersError;
    
    // Get total expenses
    const { count: totalExpenses, error: expensesError } = await supabase
      .from("expenses")
      .select("id", { count: "exact", head: true });
    
    if (expensesError) throw expensesError;
    
    // Get pending expenses
    const { count: pendingExpenses, error: pendingError } = await supabase
      .from("expenses")
      .select("id", { count: "exact", head: true })
      .eq("status", "submitted");
    
    if (pendingError) throw pendingError;
    
    // Get total categories
    const { count: categories, error: categoriesError } = await supabase
      .from("expense_categories")
      .select("id", { count: "exact", head: true });
    
    if (categoriesError) throw categoriesError;
    
    return {
      totalUsers: totalUsers || 0,
      totalExpenses: totalExpenses || 0,
      pendingExpenses: pendingExpenses || 0,
      categories: categories || 0
    };
  } catch (error) {
    console.error("Error getting admin stats:", error);
    return {
      totalUsers: 0,
      totalExpenses: 0,
      pendingExpenses: 0,
      categories: 0
    };
  }
}

/**
 * Get all expense categories
 * @returns Array of expense categories
 */
export async function getAllExpenseCategories(): Promise<ExpenseCategory[]> {
  try {
    const { data, error } = await supabase
      .from("expense_categories")
      .select("*")
      .order("name");
    
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error("Error fetching expense categories:", error);
    return [];
  }
}

/**
 * Create a new expense category
 * @param categoryData Category data
 * @returns boolean indicating success or failure
 */
export async function createExpenseCategory(categoryData: CategoryData): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("expense_categories")
      .insert(categoryData);
    
    return !error;
  } catch (error) {
    console.error("Error creating expense category:", error);
    return false;
  }
}

/**
 * Update an existing expense category
 * @param id Category ID
 * @param categoryData Updated category data
 * @returns boolean indicating success or failure
 */
export async function updateExpenseCategory(
  id: string, 
  categoryData: CategoryData
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("expense_categories")
      .update(categoryData)
      .eq("id", id);
    
    return !error;
  } catch (error) {
    console.error("Error updating expense category:", error);
    return false;
  }
}

/**
 * Get all user profiles
 * @returns Array of user profiles
 */
export async function getAllUserProfiles() {
  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .order("first_name");
    
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error("Error fetching user profiles:", error);
    return [];
  }
}

/**
 * Update a user profile
 * @param userId User ID
 * @param profileData Updated profile data
 * @returns boolean indicating success or failure
 */
export async function updateUserProfile(
  userId: string, 
  profileData: UserProfileData
): Promise<boolean> {
  try {
    console.log("Updating user profile with ID:", userId, "and data:", profileData);
    // Make sure we're using the expense schema as specified in custom instructions
    const { error } = await supabase
      .from("profiles")
      .update(profileData)
      .eq("id", userId);
    
    if (error) {
      console.error("Error in updateUserProfile:", error);
      return false;
    }
    return true;
  } catch (error) {
    console.error("Error updating user profile:", error);
    return false;
  }
}

/**
 * Delete a user profile
 * @param userId User ID
 * @returns boolean indicating success or failure
 */
export async function deleteUserProfile(userId: string): Promise<boolean> {
  try {
    console.log("Deleting user profile with ID:", userId);
    // This could be more complex in a real app (archiving instead of deleting)
    const { error } = await supabase
      .from("profiles")
      .delete()
      .eq("id", userId);
    
    if (error) {
      console.error("Error in deleteUserProfile:", error);
      return false;
    }
    return true;
  } catch (error) {
    console.error("Error deleting user profile:", error);
    return false;
  }
}

/**
 * Get all spending group policies
 * @returns Array of spending group policies
 */
export async function getSpendingGroupPolicies() {
  try {
    // Make sure we're using the expense schema as specified in custom instructions
    const { data, error } = await supabase
      .from("spending_groups")
      .select("*")
      .order("name");
    
    if (error) {
      console.error("Error fetching spending groups:", error);
      throw error;
    }
    return data || [];
  } catch (error) {
    console.error("Error fetching spending groups:", error);
    return [];
  }
}

/**
 * Set a user's spending policy
 * @param userId User ID
 * @param groupId Spending group ID
 * @returns boolean indicating success or failure
 */
export async function setUserSpendingPolicy(
  userId: string, 
  groupId: string
): Promise<boolean> {
  try {
    // Update user profile with the spending policy ID
    const { error } = await supabase
      .from("profiles")
      .update({ spending_policy_id: groupId })
      .eq("id", userId);
      
    return !error;
  } catch (error) {
    console.error("Error setting user spending policy:", error);
    return false;
  }
}

/**
 * Get expense history by expense ID
 * @param expenseId Expense ID
 * @returns Array of expense history entries
 */
export async function getExpenseHistory(expenseId: string) {
  try {
    const { data, error } = await supabase
      .from("expense_history")
      .select(`
        *,
        profiles:user_id (
          first_name,
          last_name
        )
      `)
      .eq("expense_id", expenseId)
      .order("created_at", { ascending: false });
    
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error("Error fetching expense history:", error);
    return [];
  }
}
