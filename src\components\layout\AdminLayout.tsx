
import { useEffect, useState } from "react";
import { useNavigate, Outlet } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { AdminNav } from "./AdminNav";
import { Loader2 } from "lucide-react";
import { useAuth } from "@/context/AuthProvider"; // Import our new auth hook

export const AdminLayout = () => {
  const navigate = useNavigate();
  // Use our centralized auth context
  const { session, isLoading } = useAuth();
  const [isAdminChecking, setIsAdminChecking] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    const checkAdmin = async () => {
      // If no session or still loading, wait
      if (!session || isLoading) {
        if (!isLoading && !session) {
          console.log("AdminLayout: No authenticated user found, redirecting to home");
          navigate("/");
        }
        return;
      }
      
      try {
        console.log("AdminLayout: Checking admin status for user:", session.user.id);
        
        // Get user role from profiles using the client with expense schema
        const { data: profile, error } = await supabase
          .from('profiles')
          .select("role")
          .eq("id", session.user.id)  // Using id to match auth.users.id
          .maybeSingle();
          
        // Log results for debugging  
        console.log("AdminLayout: Admin check profile data:", profile);
        console.log("AdminLayout: Admin check profile error:", error);
        
        // Set admin status
        const userIsAdmin = !error && profile && profile.role === "admin";
        setIsAdmin(!!userIsAdmin);
        
        // Redirect if not admin
        if (!userIsAdmin) {
          console.log("AdminLayout: User is not admin, redirecting to dashboard", { error, profile });
          navigate("/dashboard");
        } else {
          console.log("AdminLayout: User confirmed as admin");
        }
      } catch (error) {
        console.error("AdminLayout: Error checking admin status:", error);
        navigate("/dashboard");
      } finally {
        setIsAdminChecking(false);
      }
    };
    
    checkAdmin();
  }, [session, isLoading, navigate]);

  // Show loading state when checking auth or admin status
  if (isLoading || isAdminChecking) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // Only render content if user is authenticated and is an admin
  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminNav />
      <main className="container mx-auto py-6 px-4">
        <Outlet />
      </main>
    </div>
  );
};
