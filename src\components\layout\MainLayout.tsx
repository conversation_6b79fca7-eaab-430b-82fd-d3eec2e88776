
import { useEffect, useState } from "react";
import { useNavigate, Outlet } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { Navbar } from "./Navbar";
import { Loader2 } from "lucide-react";
import { useAuth } from "@/context/AuthProvider"; // Import our new auth hook

export const MainLayout = () => {
  const navigate = useNavigate();
  // Use our centralized auth context instead of local state
  const { session, isLoading } = useAuth();
  const [isProfileLoading, setIsProfileLoading] = useState(true);
  
  useEffect(() => {
    // Only check profile data if we have a session
    const checkUserProfile = async () => {
      if (!session) {
        setIsProfileLoading(false);
        return;
      }

      try {
        console.log("MainLayout: Checking user profile in expense schema");
        // Using the client configured with expense schema
        const { data: expenseProfile, error: expenseProfileError } = await supabase
          .from('profiles')
          .select('*')  // Select all columns to see full profile data
          .eq("id", session.user.id)  // Using id to match auth.users.id
          .maybeSingle();
          
        if (expenseProfileError) {
          console.error("MainLayout: Error checking expense.profiles:", expenseProfileError);
        }
        
        console.log("MainLayout: Expense profile check result:", expenseProfile);
        
        // Check if we need to create a profile in expense schema
        if (!expenseProfile) {
          console.log("MainLayout: User doesn't have a profile in expense schema");
          // This is where you could add code to create a profile if needed
          
          // Example of creating a profile in expense schema if needed:
          // const { error: createError } = await supabase
          //   .from('profiles')
          //   .insert({
          //     id: session.user.id,
          //     name: session.user.user_metadata.name || session.user.email,
          //     email: session.user.email,
          //     role: 'user'
          //   });
          
          // if (createError) {
          //   console.error("Failed to create user profile:", createError);
          // }
        }
      } catch (checkError) {
        console.error("MainLayout: Error during profile checks:", checkError);
      } finally {
        setIsProfileLoading(false);
      }
    };
    
    checkUserProfile();
  }, [session]);

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!isLoading && !session) {
      console.log("MainLayout: No active session, redirecting to home");
      navigate("/");
    }
  }, [session, isLoading, navigate]);

  // Show loading state when checking auth or profile
  if (isLoading || isProfileLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="container mx-auto py-6 px-4">
        <Outlet />
      </main>
    </div>
  );
};
