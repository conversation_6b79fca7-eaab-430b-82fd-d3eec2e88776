
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogDescription } from "@/components/ui/dialog";

// Define props type for the AI component
interface ExpenseLineItemAIProps {
  onAnalysisComplete: (data: {
    title?: string | null;
    amount?: number | null;
    date?: string | null;
    description?: string | null;
    category?: string | null;
    category_id?: string | null;
    receipt_url?: string | null;
  }) => void;
  categories: any[];
  lineItemId?: string;
}

// Steps in the AI analysis process
type AnalysisStep = 'idle' | 'uploading' | 'analyzing' | 'complete' | 'error';

export const ExpenseLineItemAI = ({ 
  onAnalysisComplete,
  categories,
  lineItemId
}: ExpenseLineItemAIProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState<AnalysisStep>('idle');
  const [progress, setProgress] = useState<string>('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [, setReceiptUrl] = useState<string | null>(null);
  
  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setProgress('File selected: ' + file.name);
      console.log("File selected:", file.name, "type:", file.type, "size:", file.size);
    }
  };
  
  // Process the receipt with AI
  const processReceipt = async () => {
    if (!selectedFile) {
      toast.error("Please select a file first");
      return;
    }
    
    try {
      // Step 1: Upload file
      setCurrentStep('uploading');
      setProgress('Uploading receipt...');
      console.log("Starting receipt upload process");
      
      // Generate a unique file path
      const timestamp = Date.now();
      const fileExt = selectedFile.name.split('.').pop();
      const userId = await getUserId();
      const filePath = `expense-receipts/${userId}/${timestamp}.${fileExt}`;
      console.log("Generated file path:", filePath);
      
      // Upload to Supabase storage
      console.log("Uploading to Supabase storage bucket: expense-imgs");
      const { data: uploadData, error: uploadError } = await supabase
        .storage
        .from('expense-imgs')
        .upload(filePath, selectedFile);
      
      if (uploadError) {
        console.error("Upload error:", uploadError);
        throw new Error(uploadError.message);
      }
      
      console.log("Upload successful:", uploadData);
      
      // Get the public URL
      const { data: urlData } = supabase
        .storage
        .from('expense-imgs')
        .getPublicUrl(filePath);
      
      const publicUrl = urlData.publicUrl;
      console.log("Receipt public URL:", publicUrl);
      setReceiptUrl(publicUrl);
      
      // Step 2: Analyze with AI
      setCurrentStep('analyzing');
      setProgress('Analyzing receipt with AI...');
      
      // Get access token for authorization
      console.log("Getting Supabase session for authorization");
      const { data: sessionData } = await supabase.auth.getSession();
      const accessToken = sessionData?.session?.access_token;
      
      if (!accessToken) {
        console.error("No access token available - user may not be authenticated");
        throw new Error("Authentication required");
      }
      
      // Send to edge function for processing
      console.log("Sending receipt to analyze-receipt edge function");
      const response = await fetch(`https://kydqxxkrxvqhasutegwq.supabase.co/functions/v1/analyze-receipt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add authorization header with access token
          'Authorization': `Bearer ${accessToken}`
        },
        body: JSON.stringify({ 
          receiptUrl: publicUrl,
          categories: categories.map(cat => ({ 
            id: cat.id,
            name: cat.name
          }))
        })
      });
      
      console.log("Edge function response status:", response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Edge function error:", errorText);
        throw new Error(`Failed to analyze receipt: ${response.statusText} (${response.status})`);
      }
      
      const result = await response.json();
      console.log("Receipt analysis result:", result);
      
      // Step 3: Complete and return results
      setCurrentStep('complete');
      setProgress('Analysis complete!');
      
      // Pass results back to parent component with receipt URL
      onAnalysisComplete({
        ...result,
        // Add the receipt URL to the results - ensure it's passed along
        receipt_url: publicUrl
      });
      
      // If we have a line item ID, update the receipt URL in the database
      if (lineItemId) {
        console.log("Updating line item receipt URL in database for ID:", lineItemId);
        await updateLineItemReceiptUrl(lineItemId, publicUrl);
      } else {
        // Log that we don't have a line item ID yet, so the URL will be stored when the form is submitted
        console.log("No lineItemId provided, receipt URL will be included in form submission");
      }
      
      // Close dialog after short delay
      setTimeout(() => {
        setIsDialogOpen(false);
        setCurrentStep('idle');
      }, 1500);
      
    } catch (error: any) {
      console.error('Error processing receipt:', error);
      setCurrentStep('error');
      setProgress(`Error: ${error.message}`);
      toast.error('Failed to process receipt: ' + error.message);
    }
  };
  
  // Helper to get current user ID
  const getUserId = async (): Promise<string> => {
    console.log("Getting current user ID");
    const { data } = await supabase.auth.getSession();
    if (!data.session?.user) {
      console.error("User not authenticated");
      throw new Error('User not authenticated');
    }
    console.log("Current user ID:", data.session.user.id);
    return data.session.user.id;
  };
  
  // Update the receipt URL in the database for an existing line item
  const updateLineItemReceiptUrl = async (lineItemId: string, receiptUrl: string) => {
    try {
      console.log("Updating receipt URL in database for line item:", lineItemId);
      console.log("Receipt URL to save:", receiptUrl);
      
      // Make sure to use the expense schema instead of public
      const { error, data } = await supabase
        .from('expense_line_items')
        .update({ receipt_url: receiptUrl })
        .eq('id', lineItemId)
        .select();
        
      if (error) {
        console.error("Error updating receipt URL:", error);
        throw error;
      }
      console.log('Receipt URL updated in database for line item:', lineItemId);
      console.log('Update response:', data);
    } catch (error) {
      console.error('Error updating receipt URL:', error);
    }
  };
  
  return (
    <>
      <Button
        variant="outline"
        onClick={() => setIsDialogOpen(true)}
        type="button"
        title="AI fill from receipt"
      >
        <Sparkles className="h-4 w-4 mr-1" />
        AI Fill
      </Button>
      
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>AI Receipt Analysis</DialogTitle>
            <DialogDescription>
              Upload a receipt to automatically extract expense details
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            {/* Step indicator */}
            <div className="flex items-center justify-center mb-4">
              <div className={`h-2 w-2 rounded-full mx-1 ${currentStep === 'idle' || currentStep === 'uploading' ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
              <div className={`h-2 w-2 rounded-full mx-1 ${currentStep === 'analyzing' ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
              <div className={`h-2 w-2 rounded-full mx-1 ${currentStep === 'complete' ? 'bg-green-500' : currentStep === 'error' ? 'bg-red-500' : 'bg-gray-300'}`}></div>
            </div>
            
            {/* File upload input */}
            {(currentStep === 'idle' || currentStep === 'error') && (
              <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-md p-6">
                <input
                  type="file"
                  accept="image/*,.pdf"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="ai-receipt-upload"
                />
                <label
                  htmlFor="ai-receipt-upload"
                  className="cursor-pointer text-center"
                >
                  <div className="flex flex-col items-center">
                    <Sparkles className="h-10 w-10 text-blue-500 mb-2" />
                    <p className="text-sm font-medium">Click to upload receipt</p>
                    <p className="text-xs text-gray-500 mt-1">
                      Supported formats: PNG, JPG, PDF
                    </p>
                  </div>
                </label>
                
                {selectedFile && (
                  <div className="mt-3 text-sm text-gray-600">
                    Selected: {selectedFile.name}
                  </div>
                )}
                
                <Button
                  onClick={processReceipt}
                  disabled={!selectedFile}
                  className="mt-4"
                >
                  <Sparkles className="mr-2 h-4 w-4" />
                  Analyze Receipt
                </Button>
              </div>
            )}
            
            {/* Processing status */}
            {(currentStep === 'uploading' || currentStep === 'analyzing') && (
              <div className="flex flex-col items-center justify-center py-8">
                <Loader2 className="h-12 w-12 animate-spin text-blue-500 mb-4" />
                <p className="text-sm font-medium">{progress}</p>
                <p className="text-xs text-gray-500 mt-2">
                  {currentStep === 'uploading' 
                    ? 'Uploading your receipt...' 
                    : 'Our AI is extracting data from your receipt...'}
                </p>
              </div>
            )}
            
            {/* Success state */}
            {currentStep === 'complete' && (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="bg-green-100 p-3 rounded-full">
                  <Sparkles className="h-12 w-12 text-green-500" />
                </div>
                <p className="text-lg font-medium mt-4">Analysis Complete!</p>
                <p className="text-sm text-gray-500 mt-2">
                  Your expense details have been filled automatically
                </p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
