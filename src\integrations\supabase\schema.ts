
// Schema type definitions for expense-related tables
// This supplements the main Database type in types.ts

import { supabase } from "./client";
// import { toast } from "sonner";

// Define expense category interface
export interface ExpenseCategory {
  id: string;
  name: string;
  active: boolean;
  description?: string | null;
  created_at?: string;
}

// Define expense line item interface
export interface ExpenseLineItem {
  id: string;
  expense_id: string;
  amount: number;
  currency: string;
  date: string;
  category_id?: string | null;
  description?: string | null;
  receipt_url?: string | null;
  created_at: string;
  updated_at: string;
}

// Define expense interface
export interface Expense {
  id: string;
  title: string;
  description: string | null;
  amount: number;
  currency: string;
  date: string;
  category_id: string | null;
  receipt_url: string | null;
  user_id: string;
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected' | 'paid';
  created_at: string;
  updated_at: string;
  profiles?: {
    first_name?: string;
    last_name?: string;
  } | null;
  lineItems?: ExpenseLineItem[]; // Add line items property
}

// Define profile interface for the expense schema
export interface ExpenseProfile {
  id: string;
  first_name?: string;
  last_name?: string;
  role: string;
  created_at?: string;
}

// Helper function to safely get user ID from Supabase auth and ensure it exists in profiles
export const getUserId = async (): Promise<string | null> => {
  try {
    // First get the authenticated user ID
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError) {
      console.error("Auth error:", authError);
      return null;
    }
    
    if (!user?.id) {
      console.error("No authenticated user found");
      return null;
    }
    
    console.log("Auth user ID:", user.id);
    
    // Check if this ID exists in the expense.profiles table (using our default client)
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', user.id)  // Using id to match auth.users.id
      .maybeSingle();
    
    if (profileError) {
      console.error("Error checking profiles:", profileError);
    } else {
      console.log("Profile check result:", profile);
    }
    
    // If we found a profile, return the ID
    if (profile && profile.id) {
      console.log("Found user in profiles with ID:", profile.id);
      return profile.id;
    }
    
    console.error("User ID not found in profiles table");
    return null;
  } catch (error) {
    console.error("Error getting valid user ID:", error);
    return null;
  }
};

// Helper function for fetching categories with proper typing
export const fetchCategories = async (): Promise<ExpenseCategory[]> => {
  try {
    console.log("Fetching expense categories");
    
    // Use the default client which already targets the expense schema
    const { data, error } = await supabase
      .from('expense_categories')
      .select('*')
      .eq('active', true);

    if (error) {
      console.error("Error fetching categories:", error);
      return [];
    }

    console.log("Fetched categories:", data);
    // Convert to our interface type
    return (data as ExpenseCategory[]) || [];
  } catch (error) {
    console.error("Exception in fetchCategories:", error);
    return [];
  }
};

// Helper function for fetching a specific expense
export const fetchExpense = async (expenseId: string): Promise<Expense | null> => {
  try {
    console.log("Fetching expense with ID:", expenseId);
    
    // Use the default client
    const { data, error } = await supabase
      .from('expenses')
      .select('*')
      .eq('id', expenseId)
      .maybeSingle();

    if (error) {
      console.error("Error fetching expense:", error);
      return null;
    }

    console.log("Fetched expense:", data);
    
    // Get line items if they exist
    try {
      const { data: lineItems, error: lineItemsError } = await supabase
        .from('expense_line_items')
        .select('*')
        .eq('expense_id', expenseId);
        
      if (!lineItemsError && lineItems) {
        return {
          ...(data as Expense),
          lineItems
        };
      }
    } catch (e) {
      console.log("No line items found or table doesn't exist yet");
    }
    
    return data as Expense | null;
  } catch (error) {
    console.error("Exception in fetchExpense:", error);
    return null;
  }
};

// Helper function for inserting an expense with proper typing
export const insertExpense = async (
  expenseData: Omit<Expense, 'id' | 'created_at' | 'updated_at' | 'status'>,
  lineItems: Omit<ExpenseLineItem, 'id' | 'created_at' | 'updated_at' | 'expense_id'>[]
): Promise<string | null> => {
  try {
    // Make sure we have a valid user ID that exists in profiles
    const userId = await getUserId();
    if (!userId) {
      throw new Error("Cannot create expense: No valid user profile found");
    }
    
    console.log("Verified user ID for expense insertion:", userId);
    
    // Calculate total amount from line items
    const totalAmount = lineItems.reduce((sum, item) => sum + Number(item.amount), 0);
    
    // Set default status to draft and include the verified user_id
    const dataWithDefaults = {
      ...expenseData,
      amount: totalAmount,
      status: 'draft' as const,
      user_id: userId, // Using the verified user ID from profiles
      // Use the first line item's currency and date if not provided in the main data
      currency: expenseData.currency || lineItems[0]?.currency || 'USD',
      date: expenseData.date || lineItems[0]?.date || new Date().toISOString().split('T')[0]
    };
    
    console.log("Inserting expense with data:", dataWithDefaults);
    
    // Use the default client to insert the main expense
    const { data, error } = await supabase
      .from('expenses')
      .insert(dataWithDefaults)
      .select('id')
      .single();

    if (error) {
      console.error("Error inserting expense:", error);
      return null;
    }

    if (!data || !data.id) {
      console.error("Failed to get ID of created expense");
      return null;
    }
    
    console.log("Successfully inserted expense with ID:", data.id);
    
    // Now insert the line items
    if (lineItems.length > 0) {
      const lineItemsWithExpenseId = lineItems.map(item => ({
        ...item,
        expense_id: data.id
      }));
      
      console.log("Inserting line items:", lineItemsWithExpenseId);
      
      const { error: lineItemError } = await supabase
        .from('expense_line_items')
        .insert(lineItemsWithExpenseId);
        
      if (lineItemError) {
        console.error("Error inserting line items:", lineItemError);
        // Consider rolling back the expense if line item insertion fails
        await supabase.from('expenses').delete().eq('id', data.id);
        return null;
      }
    }
    
    return data.id;
  } catch (error) {
    console.error("Exception in insertExpense:", error);
    return null;
  }
};

// Helper function for updating an expense with proper typing
export const updateExpense = async (
  expenseId: string, 
  expenseData: Partial<Omit<Expense, 'id' | 'created_at' | 'updated_at'>>,
  lineItems?: Partial<ExpenseLineItem>[]
): Promise<boolean> => {
  try {
    console.log("Updating expense with ID:", expenseId);
    console.log("Update data:", expenseData);
    
    // If we have line items, calculate the total amount
    if (lineItems && lineItems.length > 0) {
      const totalAmount = lineItems.reduce((sum, item) => sum + Number(item.amount || 0), 0);
      expenseData.amount = totalAmount;
      
      // Use the first line item's currency and date if not explicitly provided
      if (!expenseData.currency && lineItems[0]?.currency) {
        expenseData.currency = lineItems[0].currency;
      }
      
      if (!expenseData.date && lineItems[0]?.date) {
        expenseData.date = lineItems[0].date;
      }
    }
    
    // Use the default client to update the main expense
    const { error } = await supabase
      .from('expenses')
      .update(expenseData)
      .eq('id', expenseId);

    if (error) {
      console.error("Error updating expense:", error);
      return false;
    }
    
    // If line items are provided, update them as well
    if (lineItems && lineItems.length > 0) {
      // First, get existing line items
      const { data: existingItems, error: fetchError } = await supabase
        .from('expense_line_items')
        .select('id')
        .eq('expense_id', expenseId);
        
      if (fetchError) {
        console.error("Error fetching existing line items:", fetchError);
        return false;
      }
      
      const existingItemIds = (existingItems || []).map(item => item.id);
      
      // Items to create (those without IDs)
      const newItems = lineItems.filter(item => !item.id).map(item => ({
        ...item,
        expense_id: expenseId
      }));
      
      // Items to update (those with IDs that exist in the database)
      const itemsToUpdate = lineItems.filter(item => 
        item.id && existingItemIds.includes(item.id)
      );
      
      // IDs of items to delete (those in the database but not in the provided line items)
      const providedIds = lineItems.filter(item => item.id).map(item => item.id as string);
      const itemsToDelete = existingItemIds.filter(id => !providedIds.includes(id));
      
      // Process new items
      if (newItems.length > 0) {
        const { error: insertError } = await supabase
          .from('expense_line_items')
          .insert(newItems);
          
        if (insertError) {
          console.error("Error inserting new line items:", insertError);
          return false;
        }
      }
      
      // Process updates
      for (const item of itemsToUpdate) {
        if (!item.id) continue;
        
        const { error: updateError } = await supabase
          .from('expense_line_items')
          .update(item)
          .eq('id', item.id);
          
        if (updateError) {
          console.error("Error updating line item:", updateError);
          return false;
        }
      }
      
      // Process deletions
      if (itemsToDelete.length > 0) {
        const { error: deleteError } = await supabase
          .from('expense_line_items')
          .delete()
          .in('id', itemsToDelete);
          
        if (deleteError) {
          console.error("Error deleting line items:", deleteError);
          return false;
        }
      }
    }
    
    console.log("Successfully updated expense and line items");
    return true;
  } catch (error) {
    console.error("Exception in updateExpense:", error);
    return false;
  }
};
