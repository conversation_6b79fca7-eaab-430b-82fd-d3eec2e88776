
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
// import type { Database } from './types';

const SUPABASE_URL = "https://kydqxxkrxvqhasutegwq.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt5ZHF4eGtyeHZxaGFzdXRlZ3dxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIxMjc3NzMsImV4cCI6MjA1NzcwMzc3M30.xqB86MuRg-GIQ5bpvQ4aofF3q_6rl6k2WYQUbynAK14";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

// Create client with enhanced error handling
export const supabase = createClient(
  SUPABASE_URL, 
  SUPABASE_PUBLISHABLE_KEY,
  {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      storage: localStorage
    },
    global: {
      headers: {
        'X-Client-Info': 'OrderWay Web App'
      },
    },
    db: {
      schema: 'public',
    }
  }
);

/**
 * Helper function to search suppliers with pagination and server-side filtering
 * @param searchTerm Text to search for (minimum 3 characters for filtering)
 * @param page Page number for pagination
 * @param pageSize Number of results per page
 * @returns Promise with suppliers matching the search criteria
 */
export const searchSuppliers = async (searchTerm: string | null, page = 1, pageSize = 50) => {
  try {
    console.log(`[searchSuppliers] QUERY PARAMS - searchTerm: "${searchTerm}", page: ${page}, pageSize: ${pageSize}`);
    
    // Use the database function we created
    const { data, error } = await supabase
      .rpc('search_suppliers', {
        search_term: searchTerm || '',
        page_number: page,
        page_size: pageSize
      });
    
    if (error) {
      console.error('[searchSuppliers] ERROR:', error);
      throw error;
    }
    
    // Log the results
    const results = Array.isArray(data) ? data : [];
    console.log(`[searchSuppliers] RESULTS - Found ${results.length} suppliers`);
    
    // Log the first 5 results for inspection
    if (results.length > 0) {
      console.log('[searchSuppliers] SAMPLE RESULTS (first 5):');
      results.slice(0, 5).forEach((supplier, index) => {
        console.log(`  ${index + 1}. ID: ${supplier.id}, Name: ${supplier.name}`);
      });
    }
    
    // Return the data (or empty array if null)
    return results;
  } catch (error) {
    console.error('[searchSuppliers] EXCEPTION:', error);
    return [];
  }
};

/**
 * Helper function to search establishments with pagination and server-side filtering
 * @param searchTerm Text to search for (minimum 3 characters for filtering)
 * @param page Page number for pagination 
 * @param pageSize Number of results per page
 * @returns Promise with establishments matching the search criteria
 */
export const searchEstablishments = async (searchTerm: string | null, page = 1, pageSize = 50) => {
  try {
    // Use the database function we created
    const { data, error } = await supabase
      .rpc('search_establishments', {
        search_term: searchTerm || '',
        page_number: page,
        page_size: pageSize
      });
    
    if (error) throw error;
    
    // Return the data (or empty array if null)
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error('Error searching establishments:', error);
    return [];
  }
};

/**
 * Type definition for invoice data
 */
export interface InvoiceWithDetails {
  id: string;
  numfacture: string;
  codefournisseur: string;
  amount: number;
  taxamount: number;
  'Legal Entity': string;
  unit: string;
  'Cost Center': string;
  typefacture: string;
  subject: string | null;
  userid: string;
  status: string;
  duedatetime: string | null;
  stepname: string;
  createddatetime: string;
  delai_traitement: number | null;
  // Fields from the view join
  supplier_name?: string;
  establishment_name?: string;
}

/**
 * Helper function to fetch invoices with optimized server-side joins
 * Uses the database function approach for better type safety and performance
 */
export const fetchInvoicesWithDetails = async (options: {
  page: number;
  pageSize: number;
  filters: Record<string, any>;
  sort?: { field: string; direction: 'asc' | 'desc' };
}) => {
  try {
    const { page, pageSize, filters, sort } = options;
    
    // Call the database function with all the parameters
    const { data, error } = await supabase.rpc('fetch_invoices_with_details', {
      type_filter: filters.typefacture !== 'All' ? filters.typefacture : null,
      user_id_filter: filters.userid && filters.userid.trim() !== '' ? filters.userid : null,
      step_name_filter: filters.stepname !== 'all' ? filters.stepname : null,
      num_facture_filter: filters.numfacture && filters.numfacture.trim() !== '' ? filters.numfacture : null,
      supplier_filter: filters.codefournisseur !== 'all' ? filters.codefournisseur : null,
      unit_filter: filters.unit !== 'all' ? filters.unit : null,
      min_amount: filters.minAmount && filters.minAmount.trim() !== '' ? parseFloat(filters.minAmount) : undefined,
      max_amount: filters.maxAmount && filters.maxAmount.trim() !== '' ? parseFloat(filters.maxAmount) : undefined,
      sort_field: sort?.field || 'createddatetime',
      sort_direction: (sort?.direction || 'DESC').toUpperCase(),
      page_number: page,
      page_size: pageSize
    });
    
    if (error) throw error;
    
    if (!data || !Array.isArray(data)) {
      return { data: [], count: 0 };
    }
    
    // Extract the count from the first row
    const count = data && data.length > 0 ? Number(data[0]?.full_count) : 0;
    
    // Format the data to remove the full_count field
    const formattedData = data.map(item => {
      // Extract full_count before spreading the rest
      const { full_count, ...rest } = item;
      return rest as InvoiceWithDetails;
    });
    
    return { 
      data: formattedData,
      count: count || 0
    };
  } catch (error) {
    console.error('Error fetching invoices with details:', error);
    return { data: [], count: 0 };
  }
};

/**
 * Function to fetch all invoices for export using database function
 */
export const fetchAllInvoicesForExport = async (filters: Record<string, any>) => {
  try {
    // Call the database function with filters
    const { data, error } = await supabase.rpc('fetch_all_invoices_for_export', {
      type_filter: filters.typefacture !== 'All' ? filters.typefacture : null,
      user_id_filter: filters.userid && filters.userid.trim() !== '' ? filters.userid : null,
      step_name_filter: filters.stepname !== 'all' ? filters.stepname : null,
      num_facture_filter: filters.numfacture && filters.numfacture.trim() !== '' ? filters.numfacture : null,
      supplier_filter: filters.codefournisseur !== 'all' ? filters.codefournisseur : null,
      unit_filter: filters.unit !== 'all' ? filters.unit : null,
      min_amount: filters.minAmount && filters.minAmount.trim() !== '' ? parseFloat(filters.minAmount) : undefined,
      max_amount: filters.maxAmount && filters.maxAmount.trim() !== '' ? parseFloat(filters.maxAmount) : undefined,
      sort_field: filters.sort?.field || 'createddatetime',
      sort_direction: (filters.sort?.direction || 'DESC').toUpperCase()
    });
    
    if (error) throw error;
    
    // Return the data array or empty array if null
    return Array.isArray(data) ? data as InvoiceWithDetails[] : [];
  } catch (error) {
    console.error('Error fetching all invoices for export:', error);
    throw error; // Re-throw to allow caller to handle the error
  }
};
